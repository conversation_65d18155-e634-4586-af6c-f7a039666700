{"cells": [{"cell_type": "markdown", "id": "185c291e", "metadata": {"papermill": {"duration": 0.00421, "end_time": "2025-07-16T03:10:21.575975", "exception": false, "start_time": "2025-07-16T03:10:21.571765", "status": "completed"}, "tags": []}, "source": ["# References\n", "https://www.kaggle.com/code/snnclsr/cmi25-public-ensemble"]}, {"cell_type": "markdown", "id": "6f054f83", "metadata": {"_cell_guid": "246965b6-e352-428a-b6a3-afd4e3101787", "_uuid": "e1e9079e-a531-4b94-96a8-ee07efff2cdc", "collapsed": false, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 0.002978, "end_time": "2025-07-16T03:10:21.582473", "exception": false, "start_time": "2025-07-16T03:10:21.579495", "status": "completed"}, "tags": []}, "source": ["# References\n", "\n", "1. https://www.kaggle.com/code/hideyukizushi/cmi25-imu-thm-tof-tf-blendingmodel-lb-82\n", "2. https://www.kaggle.com/code/wasupandceacar/lb-0-82-5fold-single-bert-model"]}, {"cell_type": "markdown", "id": "206d70a2", "metadata": {"_cell_guid": "a4d23588-2c6d-4a6b-bff7-278e3792f557", "_uuid": "35446c71-3c04-4ea7-bb72-fd2280265520", "collapsed": false, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 0.002929, "end_time": "2025-07-16T03:10:21.588539", "exception": false, "start_time": "2025-07-16T03:10:21.585610", "status": "completed"}, "tags": []}, "source": ["# Model 1"]}, {"cell_type": "code", "execution_count": 1, "id": "f289cfd9", "metadata": {"_cell_guid": "e28baf78-1483-43c8-99c6-79fa005c6ee0", "_uuid": "ce7454ad-b4eb-4fac-9b37-9c3e580dd6cd", "collapsed": false, "execution": {"iopub.execute_input": "2025-07-16T03:10:21.596467Z", "iopub.status.busy": "2025-07-16T03:10:21.596210Z", "iopub.status.idle": "2025-07-16T03:10:48.990588Z", "shell.execute_reply": "2025-07-16T03:10:48.989781Z"}, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 27.400208, "end_time": "2025-07-16T03:10:48.991907", "exception": false, "start_time": "2025-07-16T03:10:21.591699", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-16 03:10:25.272814: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "E0000 00:00:1752635425.440402      20 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "E0000 00:00:1752635425.489416      20 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n"]}, {"name": "stdout", "output_type": "stream", "text": ["▶ imports ready · tensorflow 2.18.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["I0000 00:00:1752635436.557212      20 gpu_device.cc:2022] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 13942 MB memory:  -> device: 0, name: Tesla T4, pci bus id: 0000:00:04.0, compute capability: 7.5\n", "I0000 00:00:1752635436.557922      20 gpu_device.cc:2022] Created device /job:localhost/replica:0/task:0/device:GPU:1 with 13942 MB memory:  -> device: 1, name: Tesla T4, pci bus id: 0000:00:05.0, compute capability: 7.5\n"]}, {"name": "stdout", "output_type": "stream", "text": ["▶ INFERENCE MODE – loading artefacts from /kaggle/input/cmi-d-111\n", "  Loading models for ensemble inference...\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_0.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_1.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_2.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_3.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_4.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_5.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_6.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_7.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_8.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/D-111_9.h5\n", "--------------------------------------------------\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_0.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_1.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_2.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_3.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_4.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_5.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_6.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_7.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_8.h5\n", ">>>LoadModel>>> /kaggle/input/cmi-d-111/v0629_9.h5\n", "--------------------------------------------------\n", "[INFO]NumUseModels:20\n"]}], "source": ["import os, json, joblib, numpy as np, pandas as pd\n", "import random\n", "from pathlib import Path\n", "import warnings \n", "warnings.filterwarnings(\"ignore\")\n", "\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder\n", "from sklearn.utils.class_weight import compute_class_weight\n", "\n", "from tensorflow.keras.utils import Sequence, to_categorical, pad_sequences\n", "from tensorflow.keras.models import Model, load_model\n", "from tensorflow.keras.layers import (\n", "    Input, Conv1D, BatchNormalization, Activation, add, MaxPooling1D, Dropout,\n", "    Bidirectional, LSTM, GlobalAveragePooling1D, Dense, Multiply, Reshape,\n", "    Lambda, Concatenate, GRU, GaussianNoise\n", ")\n", "from tensorflow.keras.regularizers import l2\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.callbacks import EarlyStopping\n", "from tensorflow.keras import backend as K\n", "import tensorflow as tf\n", "import polars as pl\n", "from sklearn.model_selection import StratifiedGroupKFold\n", "from scipy.spatial.transform import Rotation as R\n", "\n", "def seed_everything(seed):\n", "    os.environ['PYTHONHASHSEED'] = str(seed)\n", "    random.seed(seed)\n", "    np.random.seed(seed)\n", "    tf.random.set_seed(seed)\n", "    tf.experimental.numpy.random.seed(seed)\n", "    os.environ['TF_CUDNN_DETERMINISTIC'] = '1'\n", "    os.environ['TF_DETERMINISTIC_OPS'] = '1'\n", "\n", "seed_everything(seed=42)\n", "# (Competition metric will only be imported when TRAINing)\n", "TRAIN = False                     # ← set to True when you want to train\n", "RAW_DIR = Path(\"/kaggle/input/cmi-detect-behavior-with-sensor-data\")\n", "PRETRAINED_DIR = Path(\"/kaggle/input/cmi-d-111\")\n", "EXPORT_DIR = Path(\"./\")                                    # artefacts will be saved here\n", "BATCH_SIZE = 64\n", "PAD_PERCENTILE = 95\n", "LR_INIT = 5e-4\n", "WD = 3e-3\n", "MIXUP_ALPHA = 0.4\n", "EPOCHS = 160\n", "PATIENCE = 40\n", "\n", "print(\"▶ imports ready · tensorflow\", tf.__version__)\n", "\n", "#Tensor Manipulations\n", "def time_sum(x):\n", "    return K.sum(x, axis=1)\n", "\n", "def squeeze_last_axis(x):\n", "    return tf.squeeze(x, axis=-1)\n", "\n", "def expand_last_axis(x):\n", "    return tf.expand_dims(x, axis=-1)\n", "\n", "def se_block(x, reduction=8):\n", "    ch = x.shape[-1]\n", "    se = GlobalAveragePooling1D()(x)\n", "    se = Dense(ch // reduction, activation='relu')(se)\n", "    se = Dense(ch, activation='sigmoid')(se)\n", "    se = Reshape((1, ch))(se)\n", "    return Multiply()([x, se])\n", "\n", "# Residual CNN Block with SE\n", "def residual_se_cnn_block(x, filters, kernel_size, pool_size=2, drop=0.3, wd=1e-4):\n", "    shortcut = x\n", "    for _ in range(2):\n", "        x = Conv1D(filters, kernel_size, padding='same', use_bias=False,\n", "                   kernel_regularizer=l2(wd))(x)\n", "        x = BatchNormalization()(x)\n", "        x = Activation('relu')(x)\n", "    x = se_block(x)\n", "    if shortcut.shape[-1] != filters:\n", "        shortcut = Conv1D(filters, 1, padding='same', use_bias=False,\n", "                          kernel_regularizer=l2(wd))(shortcut)\n", "        shortcut = BatchNormalization()(shortcut)\n", "    x = add([x, shortcut])\n", "    x = Activation('relu')(x)\n", "    x = MaxPooling1D(pool_size)(x)\n", "    x = Dropout(drop)(x)\n", "    return x\n", "\n", "def attention_layer(inputs):\n", "    score = Dense(1, activation='tanh')(inputs)\n", "    score = Lambda(squeeze_last_axis)(score)\n", "    weights = Activation('softmax')(score)\n", "    weights = Lambda(expand_last_axis)(weights)\n", "    context = Multiply()([inputs, weights])\n", "    context = Lambda(time_sum)(context)\n", "    return context\n", "\n", "# Normalizes and cleans the time series sequence. \n", "\n", "def preprocess_sequence(df_seq: pd.DataFrame, feature_cols: list[str], scaler: StandardScaler):\n", "    mat = df_seq[feature_cols].ffill().bfill().fillna(0).values\n", "    return scaler.transform(mat).astype('float32')\n", "\n", "# MixUp the data argumentation in order to regularize the neural network. \n", "\n", "class MixupGenerator(Sequence):\n", "    def __init__(self, X, y, batch_size, alpha=0.2):\n", "        self.X, self.y = X, y\n", "        self.batch = batch_size\n", "        self.alpha = alpha\n", "        self.indices = np.arange(len(X))\n", "    def __len__(self):\n", "        return int(np.ceil(len(self.X) / self.batch))\n", "    def __getitem__(self, i):\n", "        idx = self.indices[i*self.batch:(i+1)*self.batch]\n", "        Xb, yb = self.X[idx], self.y[idx]\n", "        lam = np.random.beta(self.alpha, self.alpha)\n", "        perm = np.random.permutation(len(Xb))\n", "        X_mix = lam * Xb + (1-lam) * Xb[perm]\n", "        y_mix = lam * yb + (1-lam) * yb[perm]\n", "        return X_mix, y_mix\n", "    def on_epoch_end(self):\n", "        np.random.shuffle(self.indices)\n", "\n", "\n", "def remove_gravity_from_acc(acc_data, rot_data):\n", "\n", "    if isinstance(acc_data, pd.DataFrame):\n", "        acc_values = acc_data[['acc_x', 'acc_y', 'acc_z']].values\n", "    else:\n", "        acc_values = acc_data\n", "\n", "    if isinstance(rot_data, pd.DataFrame):\n", "        quat_values = rot_data[['rot_x', 'rot_y', 'rot_z', 'rot_w']].values\n", "    else:\n", "        quat_values = rot_data\n", "\n", "    num_samples = acc_values.shape[0]\n", "    linear_accel = np.zeros_like(acc_values)\n", "    \n", "    gravity_world = np.array([0, 0, 9.81])\n", "\n", "    for i in range(num_samples):\n", "        if np.all(np.isnan(quat_values[i])) or np.all(np.isclose(quat_values[i], 0)):\n", "            linear_accel[i, :] = acc_values[i, :] \n", "            continue\n", "\n", "        try:\n", "            rotation = R.from_quat(quat_values[i])\n", "            gravity_sensor_frame = rotation.apply(gravity_world, inverse=True)\n", "            linear_accel[i, :] = acc_values[i, :] - gravity_sensor_frame\n", "        except ValueError:\n", "             linear_accel[i, :] = acc_values[i, :]\n", "             \n", "    return linear_accel\n", "\n", "def calculate_angular_velocity_from_quat(rot_data, time_delta=1/200): # Assuming 200Hz sampling rate\n", "    if isinstance(rot_data, pd.DataFrame):\n", "        quat_values = rot_data[['rot_x', 'rot_y', 'rot_z', 'rot_w']].values\n", "    else:\n", "        quat_values = rot_data\n", "\n", "    num_samples = quat_values.shape[0]\n", "    angular_vel = np.zeros((num_samples, 3))\n", "\n", "    for i in range(num_samples - 1):\n", "        q_t = quat_values[i]\n", "        q_t_plus_dt = quat_values[i+1]\n", "\n", "        if np.all(np.isnan(q_t)) or np.all(np.isclose(q_t, 0)) or \\\n", "           np.all(np.isnan(q_t_plus_dt)) or np.all(np.isclose(q_t_plus_dt, 0)):\n", "            continue\n", "\n", "        try:\n", "            rot_t = R.from_quat(q_t)\n", "            rot_t_plus_dt = R.from_quat(q_t_plus_dt)\n", "\n", "            # Calculate the relative rotation\n", "            delta_rot = rot_t.inv() * rot_t_plus_dt\n", "            \n", "            # Convert delta rotation to angular velocity vector\n", "            # The rotation vector (Euler axis * angle) scaled by 1/dt\n", "            # is a good approximation for small delta_rot\n", "            angular_vel[i, :] = delta_rot.as_rotvec() / time_delta\n", "        except ValueError:\n", "            # If quaternion is invalid, angular velocity remains zero\n", "            pass\n", "            \n", "    return angular_vel\n", "    \n", "def calculate_angular_distance(rot_data):\n", "    if isinstance(rot_data, pd.DataFrame):\n", "        quat_values = rot_data[['rot_x', 'rot_y', 'rot_z', 'rot_w']].values\n", "    else:\n", "        quat_values = rot_data\n", "\n", "    num_samples = quat_values.shape[0]\n", "    angular_dist = np.zeros(num_samples)\n", "\n", "    for i in range(num_samples - 1):\n", "        q1 = quat_values[i]\n", "        q2 = quat_values[i+1]\n", "\n", "        if np.all(np.isnan(q1)) or np.all(np.isclose(q1, 0)) or \\\n", "           np.all(np.isnan(q2)) or np.all(np.isclose(q2, 0)):\n", "            angular_dist[i] = 0 # Или np.nan, в зависимости от желаемого поведения\n", "            continue\n", "        try:\n", "            # Преобразование кватернионов в объекты Rotation\n", "            r1 = R.from_quat(q1)\n", "            r2 = R.from_quat(q2)\n", "\n", "            # Вычисление углового расстояния: 2 * arccos(|real(p * q*)|)\n", "            # где p* - сопряженный кватернион q\n", "            # В scipy.spatial.transform.Rotation, r1.inv() * r2 дает относительное вращение.\n", "            # Угол этого относительного вращения - это и есть угловое расстояние.\n", "            relative_rotation = r1.inv() * r2\n", "            \n", "            # Угол rotation vector соответствует угловому расстоянию\n", "            # Норма rotation vector - это угол в радианах\n", "            angle = np.linalg.norm(relative_rotation.as_rotvec())\n", "            angular_dist[i] = angle\n", "        except ValueError:\n", "            angular_dist[i] = 0 # В случае недействительных кватернионов\n", "            pass\n", "            \n", "    return angular_dist\n", "\n", "def build_two_branch_model(pad_len, imu_dim, tof_dim, n_classes, wd=1e-4):\n", "    inp = Input(shape=(pad_len, imu_dim+tof_dim))\n", "    imu = Lambda(lambda t: t[:, :, :imu_dim])(inp)\n", "    tof = Lambda(lambda t: t[:, :, imu_dim:])(inp)\n", "\n", "    # IMU deep branch\n", "    x1 = residual_se_cnn_block(imu, 64, 3, drop=0.1, wd=wd)\n", "    x1 = residual_se_cnn_block(x1, 128, 5, drop=0.1, wd=wd)\n", "\n", "    # TOF/Thermal lighter branch\n", "    x2 = Conv1D(64, 3, padding='same', use_bias=False, kernel_regularizer=l2(wd))(tof)\n", "    x2 = BatchNormalization()(x2); x2 = Activation('relu')(x2)\n", "    x2 = MaxPooling1D(2)(x2); x2 = Dropout(0.2)(x2)\n", "    x2 = Conv1D(128, 3, padding='same', use_bias=False, kernel_regularizer=l2(wd))(x2)\n", "    x2 = BatchNormalization()(x2); x2 = Activation('relu')(x2)\n", "    x2 = MaxPooling1D(2)(x2); x2 = Dropout(0.2)(x2)\n", "\n", "    merged = Concatenate()([x1, x2])\n", "\n", "    xa = Bidirectional(LSTM(128, return_sequences=True, kernel_regularizer=l2(wd)))(merged)\n", "    xb = Bidirectional(GRU(128, return_sequences=True, kernel_regularizer=l2(wd)))(merged)\n", "    xc = GaussianNoise(0.09)(merged)\n", "    xc = Dense(16, activation='elu')(xc)\n", "    \n", "    x = Concatenate()([xa, xb, xc])\n", "    x = Dropout(0.4)(x)\n", "    x = attention_layer(x)\n", "\n", "    for units, drop in [(256, 0.5), (128, 0.3)]:\n", "        x = Dense(units, use_bias=False, kernel_regularizer=l2(wd))(x)\n", "        x = BatchNormalization()(x); x = Activation('relu')(x)\n", "        x = Dropout(drop)(x)\n", "\n", "    out = Dense(n_classes, activation='softmax', kernel_regularizer=l2(wd))(x)\n", "    return Model(inp, out)\n", "\n", "tmp_model = build_two_branch_model(127,7,325,18)\n", "print(\"▶ INFERENCE MODE – loading artefacts from\", PRETRAINED_DIR)\n", "final_feature_cols = np.load(PRETRAINED_DIR / \"feature_cols.npy\", allow_pickle=True).tolist()\n", "pad_len        = int(np.load(PRETRAINED_DIR / \"sequence_maxlen.npy\"))\n", "scaler         = joblib.load(PRETRAINED_DIR / \"scaler.pkl\")\n", "gesture_classes = np.load(PRETRAINED_DIR / \"gesture_classes.npy\", allow_pickle=True)\n", "\n", "\n", "custom_objs = {\n", "    'time_sum': time_sum, 'squeeze_last_axis': squeeze_last_axis, 'expand_last_axis': expand_last_axis,\n", "    'se_block': se_block, 'residual_se_cnn_block': residual_se_cnn_block, 'attention_layer': attention_layer,\n", "}\n", "\n", "# ----------------------------------------------------------------- #\n", "# Load any Models\n", "# * is 2 Train Model Load\n", "# ----------------------------------------------------------------- #\n", "\n", "models1 = []\n", "print(f\"  Loading models for ensemble inference...\")\n", "for fold in range(10):\n", "    MODEL_DIR = \"/kaggle/input/cmi-d-111\"\n", "    \n", "    model_path = f\"{MODEL_DIR}/D-111_{fold}.h5\"\n", "    print(\">>>LoadModel>>>\",model_path)\n", "    model = load_model(model_path, compile=False, custom_objects=custom_objs)\n", "    models1.append(model)\n", "print(\"-\"*50)\n", "\n", "for fold in range(10):\n", "    MODEL_DIR = \"/kaggle/input/cmi-d-111\"\n", "    \n", "    model_path = f\"{MODEL_DIR}/v0629_{fold}.h5\"\n", "    print(\">>>LoadModel>>>\",model_path)\n", "    model = load_model(model_path, compile=False, custom_objects=custom_objs)\n", "    models1.append(model)\n", "print(\"-\"*50)\n", "print(f\"[INFO]NumUseModels:{len(models1)}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "76a69859", "metadata": {"_cell_guid": "0c943402-c7d9-4f0d-8cc2-12dcef0b8fd2", "_uuid": "23319942-689e-4e97-b7c4-f296516431c0", "collapsed": false, "execution": {"iopub.execute_input": "2025-07-16T03:10:49.003476Z", "iopub.status.busy": "2025-07-16T03:10:49.002857Z", "iopub.status.idle": "2025-07-16T03:10:49.010818Z", "shell.execute_reply": "2025-07-16T03:10:49.010214Z"}, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 0.014082, "end_time": "2025-07-16T03:10:49.012041", "exception": false, "start_time": "2025-07-16T03:10:48.997959", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def predict1(sequence: pl.DataFrame, demographics: pl.DataFrame) -> str:\n", "    df_seq = sequence.to_pandas()\n", "    linear_accel = remove_gravity_from_acc(df_seq, df_seq)\n", "    df_seq['linear_acc_x'], df_seq['linear_acc_y'], df_seq['linear_acc_z'] = linear_accel[:, 0], linear_accel[:, 1], linear_accel[:, 2]\n", "    df_seq['linear_acc_mag'] = np.sqrt(df_seq['linear_acc_x']**2 + df_seq['linear_acc_y']**2 + df_seq['linear_acc_z']**2)\n", "    df_seq['linear_acc_mag_jerk'] = df_seq['linear_acc_mag'].diff().fillna(0)\n", "    angular_vel = calculate_angular_velocity_from_quat(df_seq)\n", "    df_seq['angular_vel_x'], df_seq['angular_vel_y'], df_seq['angular_vel_z'] = angular_vel[:, 0], angular_vel[:, 1], angular_vel[:, 2]\n", "    df_seq['angular_distance'] = calculate_angular_distance(df_seq)\n", "    \n", "    for i in range(1, 6):\n", "        pixel_cols = [f\"tof_{i}_v{p}\" for p in range(64)]; tof_data = df_seq[pixel_cols].replace(-1, np.nan)\n", "        df_seq[f'tof_{i}_mean'], df_seq[f'tof_{i}_std'], df_seq[f'tof_{i}_min'], df_seq[f'tof_{i}_max'] = tof_data.mean(axis=1), tof_data.std(axis=1), tof_data.min(axis=1), tof_data.max(axis=1)\n", "        \n", "    mat_unscaled = df_seq[final_feature_cols].ffill().bfill().fillna(0).values.astype('float32')\n", "    mat_scaled = scaler.transform(mat_unscaled)\n", "    pad_input = pad_sequences([mat_scaled], maxlen=pad_len, padding='post', truncating='post', dtype='float32')\n", "    \n", "    all_preds = [model.predict(pad_input, verbose=0)[0] for model in models1] # 主出力のみ取得\n", "    avg_pred = np.mean(all_preds, axis=0)\n", "    return avg_pred\n", "    # return str(gesture_classes[avg_pred.argmax()])"]}, {"cell_type": "markdown", "id": "7cde8e33", "metadata": {"_cell_guid": "395b6b7b-0575-4570-9835-c8f7598b30cf", "_uuid": "12e1abaa-c410-43c2-9d09-cb25e7a3a6c0", "collapsed": false, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 0.003939, "end_time": "2025-07-16T03:10:49.020121", "exception": false, "start_time": "2025-07-16T03:10:49.016182", "status": "completed"}, "tags": []}, "source": ["# Model 2"]}, {"cell_type": "code", "execution_count": 3, "id": "f385086c", "metadata": {"_cell_guid": "80dac13f-ca07-4786-b31d-0f0c72694ebe", "_uuid": "8e6b8a61-6c7d-4277-9465-f4b25b02c4f1", "collapsed": false, "execution": {"iopub.execute_input": "2025-07-16T03:10:49.030125Z", "iopub.status.busy": "2025-07-16T03:10:49.029906Z", "iopub.status.idle": "2025-07-16T03:13:32.153657Z", "shell.execute_reply": "2025-07-16T03:13:32.152866Z"}, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 163.130884, "end_time": "2025-07-16T03:13:32.155128", "exception": false, "start_time": "2025-07-16T03:10:49.024244", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Have precomputed, skip compute.\n", "\n", "交叉验证折叠统计:\n", "\n", "Fold 1:\n", "类别                                                 训练集        验证集       \n", "Above ear - pull hair                              511        127       \n", "Cheek - pinch skin                                 509        128       \n", "Drink from bottle/cup                              129        32        \n", "Eyebrow - pull hair                                510        128       \n", "Eyelash - pull hair                                512        128       \n", "Feel around in tray and pull out an object         129        32        \n", "Forehead - pull hairline                           512        128       \n", "Forehead - scratch                                 512        128       \n", "Glasses on/off                                     128        33        \n", "Neck - pinch skin                                  512        128       \n", "Neck - scratch                                     512        128       \n", "Pinch knee/leg skin                                129        32        \n", "Pull air toward your face                          381        96        \n", "<PERSON><PERSON><PERSON> knee/leg skin                              129        32        \n", "Text on phone                                      512        128       \n", "Wave hello                                         382        96        \n", "Write name in air                                  382        95        \n", "Write name on leg                                  129        32        \n", "\n", "Fold 2:\n", "类别                                                 训练集        验证集       \n", "Above ear - pull hair                              511        127       \n", "Cheek - pinch skin                                 509        128       \n", "Drink from bottle/cup                              129        32        \n", "Eyebrow - pull hair                                510        128       \n", "Eyelash - pull hair                                512        128       \n", "Feel around in tray and pull out an object         129        32        \n", "Forehead - pull hairline                           512        128       \n", "Forehead - scratch                                 512        128       \n", "Glasses on/off                                     129        32        \n", "Neck - pinch skin                                  512        128       \n", "Neck - scratch                                     512        128       \n", "Pinch knee/leg skin                                129        32        \n", "Pull air toward your face                          381        96        \n", "<PERSON><PERSON><PERSON> knee/leg skin                              129        32        \n", "Text on phone                                      512        128       \n", "Wave hello                                         382        96        \n", "Write name in air                                  382        95        \n", "Write name on leg                                  129        32        \n", "\n", "Fold 3:\n", "类别                                                 训练集        验证集       \n", "Above ear - pull hair                              510        128       \n", "Cheek - pinch skin                                 510        127       \n", "Drink from bottle/cup                              129        32        \n", "Eyebrow - pull hair                                511        127       \n", "Eyelash - pull hair                                512        128       \n", "Feel around in tray and pull out an object         129        32        \n", "Forehead - pull hairline                           512        128       \n", "Forehead - scratch                                 512        128       \n", "Glasses on/off                                     129        32        \n", "Neck - pinch skin                                  512        128       \n", "Neck - scratch                                     512        128       \n", "Pinch knee/leg skin                                128        33        \n", "Pull air toward your face                          382        95        \n", "<PERSON><PERSON><PERSON> knee/leg skin                              129        32        \n", "Text on phone                                      512        128       \n", "Wave hello                                         382        96        \n", "Write name in air                                  382        95        \n", "Write name on leg                                  128        33        \n", "\n", "Fold 4:\n", "类别                                                 训练集        验证集       \n", "Above ear - pull hair                              510        128       \n", "Cheek - pinch skin                                 510        127       \n", "Drink from bottle/cup                              129        32        \n", "Eyebrow - pull hair                                511        127       \n", "Eyelash - pull hair                                512        128       \n", "Feel around in tray and pull out an object         128        33        \n", "Forehead - pull hairline                           512        128       \n", "Forehead - scratch                                 512        128       \n", "Glasses on/off                                     129        32        \n", "Neck - pinch skin                                  512        128       \n", "Neck - scratch                                     512        128       \n", "Pinch knee/leg skin                                129        32        \n", "Pull air toward your face                          382        95        \n", "<PERSON>ratch knee/leg skin                              128        33        \n", "Text on phone                                      512        128       \n", "Wave hello                                         383        95        \n", "Write name in air                                  381        96        \n", "Write name on leg                                  129        32        \n", "\n", "Fold 5:\n", "类别                                                 训练集        验证集       \n", "Above ear - pull hair                              510        128       \n", "Cheek - pinch skin                                 510        127       \n", "Drink from bottle/cup                              128        33        \n", "Eyebrow - pull hair                                510        128       \n", "Eyelash - pull hair                                512        128       \n", "Feel around in tray and pull out an object         129        32        \n", "Forehead - pull hairline                           512        128       \n", "Forehead - scratch                                 512        128       \n", "Glasses on/off                                     129        32        \n", "Neck - pinch skin                                  512        128       \n", "Neck - scratch                                     512        128       \n", "Pinch knee/leg skin                                129        32        \n", "Pull air toward your face                          382        95        \n", "<PERSON><PERSON><PERSON> knee/leg skin                              129        32        \n", "Text on phone                                      512        128       \n", "Wave hello                                         383        95        \n", "Write name in air                                  381        96        \n", "Write name on leg                                  129        32        \n"]}], "source": ["import os\n", "import torch\n", "import kagglehub\n", "from pathlib import Path\n", "import numpy as np\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from scipy.spatial.transform import Rotation as R\n", "from collections import defaultdict\n", "from torch.utils.data import Dataset, DataLoader, Subset\n", "from tqdm.notebook import tqdm\n", "from torch.amp import autocast\n", "import pandas as pd\n", "import polars as pl\n", "from sklearn.model_selection import StratifiedKFold\n", "from sklearn.utils.class_weight import compute_class_weight\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder\n", "from transformers import BertConfig, BertModel\n", "\n", "\n", "def remove_gravity_from_acc(acc_data, rot_data):\n", "    if isinstance(acc_data, pd.DataFrame):\n", "        acc_values = acc_data[['acc_x', 'acc_y', 'acc_z']].values\n", "    else:\n", "        acc_values = acc_data\n", "    if isinstance(rot_data, pd.DataFrame):\n", "        quat_values = rot_data[['rot_x', 'rot_y', 'rot_z', 'rot_w']].values\n", "    else:\n", "        quat_values = rot_data\n", "    num_samples = acc_values.shape[0]\n", "    linear_accel = np.zeros_like(acc_values)\n", "    gravity_world = np.array([0, 0, 9.81])\n", "    for i in range(num_samples):\n", "        if np.all(np.isnan(quat_values[i])) or np.all(np.isclose(quat_values[i], 0)):\n", "            linear_accel[i, :] = acc_values[i, :] \n", "            continue\n", "        try:\n", "            rotation = R.from_quat(quat_values[i])\n", "            gravity_sensor_frame = rotation.apply(gravity_world, inverse=True)\n", "            linear_accel[i, :] = acc_values[i, :] - gravity_sensor_frame\n", "        except ValueError:\n", "             linear_accel[i, :] = acc_values[i, :]\n", "    return linear_accel\n", "\n", "def calculate_angular_velocity_from_quat(rot_data, time_delta=1/200): # Assuming 200Hz sampling rate\n", "    if isinstance(rot_data, pd.DataFrame):\n", "        quat_values = rot_data[['rot_x', 'rot_y', 'rot_z', 'rot_w']].values\n", "    else:\n", "        quat_values = rot_data\n", "    num_samples = quat_values.shape[0]\n", "    angular_vel = np.zeros((num_samples, 3))\n", "    for i in range(num_samples - 1):\n", "        q_t = quat_values[i]\n", "        q_t_plus_dt = quat_values[i+1]\n", "        if np.all(np.isnan(q_t)) or np.all(np.isclose(q_t, 0)) or \\\n", "           np.all(np.isnan(q_t_plus_dt)) or np.all(np.isclose(q_t_plus_dt, 0)):\n", "            continue\n", "        try:\n", "            rot_t = R.from_quat(q_t)\n", "            rot_t_plus_dt = R.from_quat(q_t_plus_dt)\n", "            delta_rot = rot_t.inv() * rot_t_plus_dt\n", "            angular_vel[i, :] = delta_rot.as_rotvec() / time_delta\n", "        except ValueError:\n", "            pass\n", "    return angular_vel\n", "\n", "def calculate_angular_distance(rot_data):\n", "    if isinstance(rot_data, pd.DataFrame):\n", "        quat_values = rot_data[['rot_x', 'rot_y', 'rot_z', 'rot_w']].values\n", "    else:\n", "        quat_values = rot_data\n", "    num_samples = quat_values.shape[0]\n", "    angular_dist = np.zeros(num_samples)\n", "    for i in range(num_samples - 1):\n", "        q1 = quat_values[i]\n", "        q2 = quat_values[i+1]\n", "        if np.all(np.isnan(q1)) or np.all(np.isclose(q1, 0)) or \\\n", "           np.all(np.isnan(q2)) or np.all(np.isclose(q2, 0)):\n", "            angular_dist[i] = 0\n", "            continue\n", "        try:\n", "            r1 = R.from_quat(q1)\n", "            r2 = R.from_quat(q2)\n", "            relative_rotation = r1.inv() * r2\n", "            angle = np.linalg.norm(relative_rotation.as_rotvec())\n", "            angular_dist[i] = angle\n", "        except ValueError:\n", "            angular_dist[i] = 0 # В случае недействительных кватернионов\n", "            pass\n", "    return angular_dist\n", "\n", "\n", "class CMIFeDataset(Dataset):\n", "    def __init__(self, data_path, config):\n", "        self.config = config\n", "        self.init_feature_names(data_path)\n", "        df = self.generate_features(pd.read_csv(data_path, usecols=set(self.base_cols+self.feature_cols)))\n", "        self.generate_dataset(df)\n", "\n", "    def init_feature_names(self, data_path):\n", "        self.imu_engineered_features = [\n", "            'acc_mag', 'rot_angle',\n", "            'acc_mag_jerk', 'rot_angle_vel',\n", "            'linear_acc_mag', 'linear_acc_mag_jerk',\n", "            'angular_vel_x', 'angular_vel_y', 'angular_vel_z',\n", "            'angular_distance'\n", "        ]\n", "\n", "        self.tof_mode = self.config.get(\"tof_mode\", \"stats\")\n", "        self.tof_region_stats = ['mean', 'std', 'min', 'max']\n", "        self.tof_cols = self.generate_tof_feature_names()\n", "\n", "        columns = pd.read_csv(data_path, nrows=0).columns.tolist()\n", "        imu_cols_base = ['linear_acc_x', 'linear_acc_y', 'linear_acc_z']\n", "        imu_cols_base.extend([c for c in columns if c.startswith('rot_') and c not in ['rot_angle', 'rot_angle_vel']])\n", "        self.imu_cols = list(dict.fromkeys(imu_cols_base + self.imu_engineered_features))\n", "        self.thm_cols = [c for c in columns if c.startswith('thm_')]\n", "        self.feature_cols = self.imu_cols + self.thm_cols + self.tof_cols\n", "        self.imu_dim = len(self.imu_cols)\n", "        self.thm_dim = len(self.thm_cols)\n", "        self.tof_dim = len(self.tof_cols)\n", "        self.base_cols = ['acc_x', 'acc_y', 'acc_z',\n", "                          'rot_x', 'rot_y', 'rot_z', 'rot_w',\n", "                          'sequence_id', 'subject', \n", "                          'sequence_type', 'gesture', 'orientation'] + [c for c in columns if c.startswith('thm_')] + [f\"tof_{i}_v{p}\" for i in range(1, 6) for p in range(64)]\n", "        self.fold_cols = ['subject', 'sequence_type', 'gesture', 'orientation']\n", "\n", "    def generate_tof_feature_names(self):\n", "        features = []\n", "        if self.config.get(\"tof_raw\", False):\n", "            for i in range(1, 6):\n", "                features.extend([f\"tof_{i}_v{p}\" for p in range(64)])\n", "        for i in range(1, 6):\n", "            if self.tof_mode != 0:\n", "                for stat in self.tof_region_stats:\n", "                    features.append(f'tof_{i}_{stat}')\n", "                if self.tof_mode > 1:\n", "                    for r in range(self.tof_mode):\n", "                        for stat in self.tof_region_stats:\n", "                            features.append(f'tof{self.tof_mode}_{i}_region_{r}_{stat}')\n", "                if self.tof_mode == -1:\n", "                    for mode in [2, 4, 8, 16, 32]:\n", "                        for r in range(mode):\n", "                            for stat in self.tof_region_stats:\n", "                                features.append(f'tof{mode}_{i}_region_{r}_{stat}')\n", "        return features\n", "\n", "    def compute_features(self, df):\n", "        df['acc_mag'] = np.sqrt(df['acc_x']**2 + df['acc_y']**2 + df['acc_z']**2)\n", "        df['rot_angle'] = 2 * np.arccos(df['rot_w'].clip(-1, 1))\n", "        df['acc_mag_jerk'] = df.groupby('sequence_id')['acc_mag'].diff().fillna(0)\n", "        df['rot_angle_vel'] = df.groupby('sequence_id')['rot_angle'].diff().fillna(0)\n", "            \n", "        linear_accel_list = []\n", "        for _, group in df.groupby('sequence_id'):\n", "            acc_data_group = group[['acc_x', 'acc_y', 'acc_z']]\n", "            rot_data_group = group[['rot_x', 'rot_y', 'rot_z', 'rot_w']]\n", "            linear_accel_group = remove_gravity_from_acc(acc_data_group, rot_data_group)\n", "            linear_accel_list.append(pd.DataFrame(linear_accel_group, columns=['linear_acc_x', 'linear_acc_y', 'linear_acc_z'], index=group.index))\n", "        df_linear_accel = pd.concat(linear_accel_list)\n", "        df = pd.concat([df, df_linear_accel], axis=1)\n", "        df['linear_acc_mag'] = np.sqrt(df['linear_acc_x']**2 + df['linear_acc_y']**2 + df['linear_acc_z']**2)\n", "        df['linear_acc_mag_jerk'] = df.groupby('sequence_id')['linear_acc_mag'].diff().fillna(0)\n", "    \n", "        angular_vel_list = []\n", "        for _, group in df.groupby('sequence_id'):\n", "            rot_data_group = group[['rot_x', 'rot_y', 'rot_z', 'rot_w']]\n", "            angular_vel_group = calculate_angular_velocity_from_quat(rot_data_group)\n", "            angular_vel_list.append(pd.DataFrame(angular_vel_group, columns=['angular_vel_x', 'angular_vel_y', 'angular_vel_z'], index=group.index))\n", "        df_angular_vel = pd.concat(angular_vel_list)\n", "        df = pd.concat([df, df_angular_vel], axis=1)\n", "    \n", "        angular_distance_list = []\n", "        for _, group in df.groupby('sequence_id'):\n", "            rot_data_group = group[['rot_x', 'rot_y', 'rot_z', 'rot_w']]\n", "            angular_dist_group = calculate_angular_distance(rot_data_group)\n", "            angular_distance_list.append(pd.DataFrame(angular_dist_group, columns=['angular_distance'], index=group.index))\n", "        df_angular_distance = pd.concat(angular_distance_list)\n", "        df = pd.concat([df, df_angular_distance], axis=1)\n", "\n", "        if self.tof_mode != 0:\n", "            new_columns = {}\n", "            for i in range(1, 6):\n", "                pixel_cols = [f\"tof_{i}_v{p}\" for p in range(64)]\n", "                tof_data = df[pixel_cols].replace(-1, np.nan)\n", "                new_columns.update({\n", "                    f'tof_{i}_mean': tof_data.mean(axis=1),\n", "                    f'tof_{i}_std': tof_data.std(axis=1),\n", "                    f'tof_{i}_min': tof_data.min(axis=1),\n", "                    f'tof_{i}_max': tof_data.max(axis=1)\n", "                })\n", "                if self.tof_mode > 1:\n", "                    region_size = 64 // self.tof_mode\n", "                    for r in range(self.tof_mode):\n", "                        region_data = tof_data.iloc[:, r*region_size : (r+1)*region_size]\n", "                        new_columns.update({\n", "                            f'tof{self.tof_mode}_{i}_region_{r}_mean': region_data.mean(axis=1),\n", "                            f'tof{self.tof_mode}_{i}_region_{r}_std': region_data.std(axis=1),\n", "                            f'tof{self.tof_mode}_{i}_region_{r}_min': region_data.min(axis=1),\n", "                            f'tof{self.tof_mode}_{i}_region_{r}_max': region_data.max(axis=1)\n", "                        })\n", "                if self.tof_mode == -1:\n", "                    for mode in [2, 4, 8, 16, 32]:\n", "                        region_size = 64 // mode\n", "                        for r in range(mode):\n", "                            region_data = tof_data.iloc[:, r*region_size : (r+1)*region_size]\n", "                            new_columns.update({\n", "                                f'tof{mode}_{i}_region_{r}_mean': region_data.mean(axis=1),\n", "                                f'tof{mode}_{i}_region_{r}_std': region_data.std(axis=1),\n", "                                f'tof{mode}_{i}_region_{r}_min': region_data.min(axis=1),\n", "                                f'tof{mode}_{i}_region_{r}_max': region_data.max(axis=1)\n", "                            })\n", "            df = pd.concat([df, pd.DataFrame(new_columns)], axis=1)\n", "        return df\n", "        \n", "    def generate_features(self, df):\n", "        self.le = LabelEncoder()\n", "        df['gesture_int'] = self.le.fit_transform(df['gesture'])\n", "        self.class_num = len(self.le.classes_)\n", "        \n", "        if all(c in df.columns for c in self.imu_engineered_features) and all(c in df.columns for c in self.tof_cols):\n", "            print(\"Have precomputed, skip compute.\")\n", "        else:\n", "            print(\"Not precomputed, do compute.\")\n", "            df = self.compute_features(df)\n", "\n", "        if self.config.get(\"save_precompute\", False):\n", "            df.to_csv(self.config.get(\"save_filename\", \"train.csv\"))\n", "        return df\n", "\n", "    def scale(self, data_unscaled):\n", "        scaler_function = self.config.get(\"scaler_function\", StandardScaler())\n", "        scaler = scaler_function.fit(np.concatenate(data_unscaled, axis=0))\n", "        return [scaler.transform(x) for x in data_unscaled], scaler\n", "\n", "    def pad(self, data_scaled, cols):\n", "        pad_data = np.zeros((len(data_scaled), self.pad_len, len(cols)), dtype='float32')\n", "        for i, seq in enumerate(data_scaled):\n", "            seq_len = min(len(seq), self.pad_len)\n", "            pad_data[i, :seq_len] = seq[:seq_len]\n", "        return pad_data\n", "\n", "    def get_nan_value(self, data, ratio):\n", "        max_value = data.max().max()\n", "        nan_value = -max_value * ratio\n", "        return nan_value\n", "\n", "    def generate_dataset(self, df):\n", "        seq_gp = df.groupby('sequence_id') \n", "        imu_unscaled, thm_unscaled, tof_unscaled = [], [], []\n", "        classes, lens = [], []\n", "        self.imu_nan_value = self.get_nan_value(df[self.imu_cols], self.config[\"nan_ratio\"][\"imu\"])\n", "        self.thm_nan_value = self.get_nan_value(df[self.thm_cols], self.config[\"nan_ratio\"][\"thm\"])\n", "        self.tof_nan_value = self.get_nan_value(df[self.tof_cols], self.config[\"nan_ratio\"][\"tof\"])\n", "\n", "        self.fold_feats = defaultdict(list)\n", "        for seq_id, seq_df in seq_gp:\n", "            imu_data = seq_df[self.imu_cols]\n", "            if self.config[\"fbfill\"][\"imu\"]:\n", "                imu_data = imu_data.ffill().bfill()\n", "            imu_unscaled.append(imu_data.fillna(self.imu_nan_value).values.astype('float32'))\n", "\n", "            thm_data = seq_df[self.thm_cols]\n", "            if self.config[\"fbfill\"][\"thm\"]:\n", "                thm_data = thm_data.ffill().bfill()\n", "            thm_unscaled.append(thm_data.fillna(self.thm_nan_value).values.astype('float32'))\n", "\n", "            tof_data = seq_df[self.tof_cols]\n", "            if self.config[\"fbfill\"][\"tof\"]:\n", "                tof_data = tof_data.ffill().bfill()\n", "            tof_unscaled.append(tof_data.fillna(self.tof_nan_value).values.astype('float32'))\n", "            \n", "            classes.append(seq_df['gesture_int'].iloc[0])\n", "            lens.append(len(imu_data))\n", "\n", "            for col in self.fold_cols:\n", "                self.fold_feats[col].append(seq_df[col].iloc[0])\n", "            \n", "        self.dataset_indices = classes\n", "        self.pad_len = int(np.percentile(lens, self.config.get(\"percent\", 95)))\n", "        if self.config.get(\"one_scale\", True):\n", "            x_unscaled = [np.concatenate([imu, thm, tof], axis=1) for imu, thm, tof in zip(imu_unscaled, thm_unscaled, tof_unscaled)]\n", "            x_scaled, self.x_scaler = self.scale(x_unscaled)\n", "            x = self.pad(x_scaled, self.imu_cols+self.thm_cols+self.tof_cols)\n", "            self.imu = x[..., :self.imu_dim]\n", "            self.thm = x[..., self.imu_dim:self.imu_dim+self.thm_dim]\n", "            self.tof = x[..., self.imu_dim+self.thm_dim:self.imu_dim+self.thm_dim+self.tof_dim]\n", "        else:\n", "            imu_scaled, self.imu_scaler = self.scale(imu_unscaled)\n", "            thm_scaled, self.thm_scaler = self.scale(thm_unscaled)\n", "            tof_scaled, self.tof_scaler = self.scale(tof_unscaled)\n", "            self.imu = self.pad(imu_scaled, self.imu_cols)\n", "            self.thm = self.pad(thm_scaled, self.thm_cols)\n", "            self.tof = self.pad(tof_scaled, self.tof_cols)\n", "        self.precompute_scaled_nan_values()\n", "        self.class_ = F.one_hot(torch.from_numpy(np.array(classes)).long(), num_classes=len(self.le.classes_)).float().numpy()\n", "        self.class_weight = torch.FloatTensor(compute_class_weight('balanced', classes=np.arange(len(self.le.classes_)), y=classes))\n", "\n", "    def precompute_scaled_nan_values(self):\n", "        dummy_df = pd.DataFrame(\n", "            np.array([[self.imu_nan_value]*len(self.imu_cols) + \n", "                     [self.thm_nan_value]*len(self.thm_cols) +\n", "                     [self.tof_nan_value]*len(self.tof_cols)]),\n", "            columns=self.imu_cols + self.thm_cols + self.tof_cols\n", "        )\n", "        \n", "        if self.config.get(\"one_scale\", True):\n", "            scaled = self.x_scaler.transform(dummy_df)\n", "            self.imu_scaled_nan = scaled[0, :self.imu_dim].mean()\n", "            self.thm_scaled_nan = scaled[0, self.imu_dim:self.imu_dim+self.thm_dim].mean()\n", "            self.tof_scaled_nan = scaled[0, self.imu_dim+self.thm_dim:self.imu_dim+self.thm_dim+self.tof_dim].mean()\n", "        else:\n", "            self.imu_scaled_nan = self.imu_scaler.transform(dummy_df[self.imu_cols])[0].mean()\n", "            self.thm_scaled_nan = self.thm_scaler.transform(dummy_df[self.thm_cols])[0].mean()\n", "            self.tof_scaled_nan = self.tof_scaler.transform(dummy_df[self.tof_cols])[0].mean()\n", "\n", "    def get_scaled_nan_tensors(self, imu, thm, tof):\n", "        return torch.full(imu.shape, self.imu_scaled_nan, device=imu.device), \\\n", "            torch.full(thm.shape, self.thm_scaled_nan, device=thm.device), \\\n", "            torch.full(tof.shape, self.tof_scaled_nan, device=tof.device)\n", "\n", "    def inference_process(self, sequence):\n", "        df_seq = sequence.to_pandas().copy()\n", "        if not all(c in df_seq.columns for c in self.imu_engineered_features):\n", "            df_seq['acc_mag'] = np.sqrt(df_seq['acc_x']**2 + df_seq['acc_y']**2 + df_seq['acc_z']**2)\n", "            df_seq['rot_angle'] = 2 * np.arccos(df_seq['rot_w'].clip(-1, 1))\n", "            df_seq['acc_mag_jerk'] = df_seq['acc_mag'].diff().fillna(0)\n", "            df_seq['rot_angle_vel'] = df_seq['rot_angle'].diff().fillna(0)\n", "            if all(col in df_seq.columns for col in ['acc_x', 'acc_y', 'acc_z', 'rot_x', 'rot_y', 'rot_z', 'rot_w']):\n", "                linear_accel = remove_gravity_from_acc(\n", "                    df_seq[['acc_x', 'acc_y', 'acc_z']], \n", "                    df_seq[['rot_x', 'rot_y', 'rot_z', 'rot_w']]\n", "                )\n", "                df_seq[['linear_acc_x', 'linear_acc_y', 'linear_acc_z']] = linear_accel\n", "            else:\n", "                df_seq['linear_acc_x'] = df_seq.get('acc_x', 0)\n", "                df_seq['linear_acc_y'] = df_seq.get('acc_y', 0)\n", "                df_seq['linear_acc_z'] = df_seq.get('acc_z', 0)\n", "            df_seq['linear_acc_mag'] = np.sqrt(df_seq['linear_acc_x']**2 + df_seq['linear_acc_y']**2 + df_seq['linear_acc_z']**2)\n", "            df_seq['linear_acc_mag_jerk'] = df_seq['linear_acc_mag'].diff().fillna(0)\n", "            if all(col in df_seq.columns for col in ['rot_x', 'rot_y', 'rot_z', 'rot_w']):\n", "                angular_vel = calculate_angular_velocity_from_quat(df_seq[['rot_x', 'rot_y', 'rot_z', 'rot_w']])\n", "                df_seq[['angular_vel_x', 'angular_vel_y', 'angular_vel_z']] = angular_vel\n", "            else:\n", "                df_seq[['angular_vel_x', 'angular_vel_y', 'angular_vel_z']] = 0\n", "            if all(col in df_seq.columns for col in ['rot_x', 'rot_y', 'rot_z', 'rot_w']):\n", "                df_seq['angular_distance'] = calculate_angular_distance(df_seq[['rot_x', 'rot_y', 'rot_z', 'rot_w']])\n", "            else:\n", "                df_seq['angular_distance'] = 0\n", "\n", "        if self.tof_mode != 0:\n", "            new_columns = {} \n", "            for i in range(1, 6):\n", "                pixel_cols = [f\"tof_{i}_v{p}\" for p in range(64)]\n", "                tof_data = df_seq[pixel_cols].replace(-1, np.nan)\n", "                new_columns.update({\n", "                    f'tof_{i}_mean': tof_data.mean(axis=1),\n", "                    f'tof_{i}_std': tof_data.std(axis=1),\n", "                    f'tof_{i}_min': tof_data.min(axis=1),\n", "                    f'tof_{i}_max': tof_data.max(axis=1)\n", "                })\n", "                if self.tof_mode > 1:\n", "                    region_size = 64 // self.tof_mode\n", "                    for r in range(self.tof_mode):\n", "                        region_data = tof_data.iloc[:, r*region_size : (r+1)*region_size]\n", "                        new_columns.update({\n", "                            f'tof{self.tof_mode}_{i}_region_{r}_mean': region_data.mean(axis=1),\n", "                            f'tof{self.tof_mode}_{i}_region_{r}_std': region_data.std(axis=1),\n", "                            f'tof{self.tof_mode}_{i}_region_{r}_min': region_data.min(axis=1),\n", "                            f'tof{self.tof_mode}_{i}_region_{r}_max': region_data.max(axis=1)\n", "                        })\n", "                if self.tof_mode == -1:\n", "                    for mode in [2, 4, 8, 16, 32]:\n", "                        region_size = 64 // mode\n", "                        for r in range(mode):\n", "                            region_data = tof_data.iloc[:, r*region_size : (r+1)*region_size]\n", "                            new_columns.update({\n", "                                f'tof{mode}_{i}_region_{r}_mean': region_data.mean(axis=1),\n", "                                f'tof{mode}_{i}_region_{r}_std': region_data.std(axis=1),\n", "                                f'tof{mode}_{i}_region_{r}_min': region_data.min(axis=1),\n", "                                f'tof{mode}_{i}_region_{r}_max': region_data.max(axis=1)\n", "                            })\n", "            df_seq = pd.concat([df_seq, pd.DataFrame(new_columns)], axis=1)\n", "        \n", "        imu_unscaled = df_seq[self.imu_cols]\n", "        if self.config[\"fbfill\"][\"imu\"]:\n", "            imu_unscaled = imu_unscaled.ffill().bfill()\n", "        imu_unscaled = imu_unscaled.fillna(self.imu_nan_value).values.astype('float32')\n", "\n", "        thm_unscaled = df_seq[self.thm_cols]\n", "        if self.config[\"fbfill\"][\"thm\"]:\n", "            thm_unscaled = thm_unscaled.ffill().bfill()\n", "        thm_unscaled = thm_unscaled.fillna(self.thm_nan_value).values.astype('float32')\n", "\n", "        tof_unscaled = df_seq[self.tof_cols]\n", "        if self.config[\"fbfill\"][\"tof\"]:\n", "            tof_unscaled = tof_unscaled.ffill().bfill()\n", "        tof_unscaled = tof_unscaled.fillna(self.tof_nan_value).values.astype('float32')\n", "        \n", "        if self.config.get(\"one_scale\", True):\n", "            x_unscaled = np.concatenate([imu_unscaled, thm_unscaled, tof_unscaled], axis=1)\n", "            x_scaled = self.x_scaler.transform(x_unscaled)\n", "            imu_scaled = x_scaled[..., :self.imu_dim]\n", "            thm_scaled = x_scaled[..., self.imu_dim:self.imu_dim+self.thm_dim]\n", "            tof_scaled = x_scaled[..., self.imu_dim+self.thm_dim:self.imu_dim+self.thm_dim+self.tof_dim]\n", "        else:\n", "            imu_scaled = self.imu_scaler.transform(imu_unscaled)\n", "            thm_scaled = self.thm_scaler.transform(thm_unscaled)\n", "            tof_scaled = self.tof_scaler.transform(tof_unscaled)\n", "\n", "        combined = np.concatenate([imu_scaled, thm_scaled, tof_scaled], axis=1)\n", "        padded = np.zeros((self.pad_len, combined.shape[1]), dtype='float32')\n", "        seq_len = min(combined.shape[0], self.pad_len)\n", "        padded[:seq_len] = combined[:seq_len]\n", "        imu = padded[..., :self.imu_dim]\n", "        thm = padded[..., self.imu_dim:self.imu_dim+self.thm_dim]\n", "        tof = padded[..., self.imu_dim+self.thm_dim:self.imu_dim+self.thm_dim+self.tof_dim]\n", "        \n", "        return torch.from_numpy(imu).float().unsqueeze(0), torch.from_numpy(thm).float().unsqueeze(0), torch.from_numpy(tof).float().unsqueeze(0)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.imu[idx], self.thm[idx], self.tof[idx], self.class_[idx]\n", "\n", "    def __len__(self):\n", "        return len(self.class_)\n", "\n", "class CMIFoldDataset:\n", "    def __init__(self, data_path, config, full_dataset_function, n_folds=5, random_seed=0):\n", "        self.full_dataset = full_dataset_function(data_path=data_path, config=config)\n", "        self.imu_dim = self.full_dataset.imu_dim\n", "        self.thm_dim = self.full_dataset.thm_dim\n", "        self.tof_dim = self.full_dataset.tof_dim\n", "        self.le = self.full_dataset.le\n", "        self.class_names = self.full_dataset.le.classes_\n", "        self.class_weight = self.full_dataset.class_weight\n", "        self.n_folds = n_folds\n", "        self.skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=random_seed)\n", "        self.folds = list(self.skf.split(np.arange(len(self.full_dataset)), np.array(self.full_dataset.dataset_indices)))\n", "    \n", "    def get_fold_datasets(self, fold_idx):\n", "        if self.folds is None or fold_idx >= self.n_folds:\n", "            return None, None\n", "        fold_train_idx, fold_valid_idx = self.folds[fold_idx]\n", "        return Subset(self.full_dataset, fold_train_idx), Subset(self.full_dataset, fold_valid_idx)\n", "\n", "    def print_fold_stats(self):\n", "        def get_label_counts(subset):\n", "            counts = {name: 0 for name in self.class_names}\n", "            if subset is None:\n", "                return counts\n", "            for idx in subset.indices:\n", "                label_idx = self.full_dataset.dataset_indices[idx]\n", "                counts[self.class_names[label_idx]] += 1\n", "            return counts\n", "        \n", "        print(\"\\n交叉验证折叠统计:\")\n", "        for fold_idx in range(self.n_folds):\n", "            train_fold, valid_fold = self.get_fold_datasets(fold_idx)\n", "            train_counts = get_label_counts(train_fold)\n", "            valid_counts = get_label_counts(valid_fold)\n", "                \n", "            print(f\"\\nFold {fold_idx + 1}:\")\n", "            print(f\"{'类别':<50} {'训练集':<10} {'验证集':<10}\")\n", "            for name in self.class_names:\n", "                print(f\"{name:<50} {train_counts[name]:<10} {valid_counts[name]:<10}\")\n", "\n", "\n", "class SEBlock(nn.Module):\n", "    def __init__(self, channels, reduction = 8):\n", "        super().__init__()\n", "        self.fc1 = nn.Linear(channels, channels // reduction, bias=True)\n", "        self.fc2 = nn.Linear(channels // reduction, channels, bias=True)\n", "        self.sigmoid = nn.Sigmoid()\n", "\n", "    def forward(self, x):\n", "        # x: (B, C, L)\n", "        se = F.adaptive_avg_pool1d(x, 1).squeeze(-1)      # -> (B, C)\n", "        se = F.relu(self.fc1(se), inplace=True)          # -> (B, C//r)\n", "        se = self.sigmoid(self.fc2(se)).unsqueeze(-1)    # -> (B, C, 1)\n", "        return x * se                \n", "\n", "class ResNetSEBlock(nn.Module):\n", "    def __init__(self, in_channels, out_channels, wd = 1e-4):\n", "        super().__init__()\n", "        self.conv1 = nn.Conv1d(in_channels, out_channels,\n", "                               kernel_size=3, padding=1, bias=False)\n", "        self.bn1 = nn.BatchNorm1d(out_channels)\n", "        self.conv2 = nn.Conv1d(out_channels, out_channels,\n", "                               kernel_size=3, padding=1, bias=False)\n", "        self.bn2 = nn.BatchNorm1d(out_channels)\n", "        # SE\n", "        self.se = SEBlock(out_channels)\n", "        \n", "        if in_channels != out_channels:\n", "            self.shortcut = nn.Sequential(\n", "                nn.Conv1d(in_channels, out_channels, kernel_size=1,\n", "                          padding=0, bias=False),\n", "                nn.BatchNorm1d(out_channels)\n", "            )\n", "        else:\n", "            self.shortcut = nn.Identity()\n", "\n", "        self.relu = nn.ReLU(inplace=True)\n", "\n", "    def forward(self, x) :\n", "        identity = self.shortcut(x)              # (B, out, L)\n", "        out = self.relu(self.bn1(self.conv1(x)))\n", "        out = self.bn2(self.conv2(out))\n", "        out = self.se(out)                       # (B, out, L)\n", "        out = out + identity\n", "        return self.relu(out)\n", "\n", "class CMIModel(nn.Module):\n", "    def __init__(self, imu_dim, thm_dim, tof_dim, n_classes, **kwargs):\n", "        super().__init__()\n", "        self.imu_branch = nn.Sequential(\n", "            self.residual_se_cnn_block(imu_dim, kwargs[\"imu1_channels\"], kwargs[\"imu1_layers\"],\n", "                                       drop=kwargs[\"imu1_dropout\"]),\n", "            self.residual_se_cnn_block(kwargs[\"imu1_channels\"], kwargs[\"feat_dim\"], kwargs[\"imu2_layers\"],\n", "                                       drop=kwargs[\"imu2_dropout\"])\n", "        )\n", "\n", "        self.thm_branch = nn.Sequential(\n", "            nn.Conv1d(thm_dim, kwargs[\"thm1_channels\"], kernel_size=3, padding=1, bias=False),\n", "            nn.BatchNorm1d(kwargs[\"thm1_channels\"]),\n", "            nn.ReLU(inplace=True),\n", "            nn.MaxPool1d(2, ceil_mode=True),\n", "            nn.Dropout(kwargs[\"thm1_dropout\"]),\n", "            \n", "            nn.Conv1d(kwargs[\"thm1_channels\"], kwargs[\"feat_dim\"], kernel_size=3, padding=1, bias=False),\n", "            nn.BatchNorm1d(kwargs[\"feat_dim\"]),\n", "            nn.ReLU(inplace=True),\n", "            nn.MaxPool1d(2, ceil_mode=True),\n", "            nn.Dropout(kwargs[\"thm2_dropout\"])\n", "        )\n", "        \n", "        self.tof_branch = nn.Sequential(\n", "            nn.Conv1d(tof_dim, kwargs[\"tof1_channels\"], kernel_size=3, padding=1, bias=False),\n", "            nn.BatchNorm1d(kwargs[\"tof1_channels\"]),\n", "            nn.ReLU(inplace=True),\n", "            nn.MaxPool1d(2, ceil_mode=True),\n", "            nn.Dropout(kwargs[\"tof1_dropout\"]),\n", "            \n", "            nn.Conv1d(kwargs[\"tof1_channels\"], kwargs[\"feat_dim\"], kernel_size=3, padding=1, bias=False),\n", "            nn.BatchNorm1d(kwargs[\"feat_dim\"]),\n", "            nn.ReLU(inplace=True),\n", "            nn.MaxPool1d(2, ceil_mode=True),\n", "            nn.Dropout(kwargs[\"tof2_dropout\"])\n", "        )\n", "\n", "        self.cls_token = nn.Parameter(torch.zeros(1, 1, kwargs[\"feat_dim\"]))\n", "        self.bert = BertModel(BertConfig(\n", "            hidden_size=kwargs[\"feat_dim\"],\n", "            num_hidden_layers=kwargs[\"bert_layers\"],\n", "            num_attention_heads=kwargs[\"bert_heads\"],\n", "            intermediate_size=kwargs[\"feat_dim\"]*4\n", "        ))\n", "        \n", "        self.classifier = nn.Sequential(\n", "            nn.Linear(kwargs[\"feat_dim\"], kwargs[\"cls1_channels\"], bias=False),\n", "            nn.BatchNorm1d(kwargs[\"cls1_channels\"]),\n", "            nn.ReLU(inplace=True),\n", "            nn.Dropout(kwargs[\"cls1_dropout\"]),\n", "            nn.Linear(kwargs[\"cls1_channels\"], kwargs[\"cls2_channels\"], bias=False),\n", "            nn.BatchNorm1d(kwargs[\"cls2_channels\"]),\n", "            nn.ReLU(inplace=True),\n", "            nn.Dropout(kwargs[\"cls2_dropout\"]),\n", "            nn.Linear(kwargs[\"cls2_channels\"], n_classes)\n", "        )\n", "    \n", "    def residual_se_cnn_block(self, in_channels, out_channels, num_layers, pool_size=2, drop=0.3, wd=1e-4):\n", "        return nn.Sequential(\n", "            *[ResNetSEBlock(in_channels=in_channels, out_channels=in_channels) for i in range(num_layers)],\n", "            ResNetSEBlock(in_channels, out_channels, wd=wd),\n", "            nn.MaxPool1d(pool_size),\n", "            nn.Dropout(drop)\n", "        )\n", "    \n", "    def forward(self, imu, thm, tof):\n", "        imu_feat = self.imu_branch(imu.permute(0, 2, 1))\n", "        thm_feat = self.thm_branch(thm.permute(0, 2, 1))\n", "        tof_feat = self.tof_branch(tof.permute(0, 2, 1))\n", "        \n", "        bert_input = torch.cat([imu_feat, thm_feat, tof_feat], dim=-1).permute(0, 2, 1)\n", "        cls_token = self.cls_token.expand(bert_input.size(0), -1, -1)  # (B,1,H)\n", "        bert_input = torch.cat([cls_token, bert_input], dim=1)  # (B,T+1,H)\n", "        outputs = self.bert(inputs_embeds=bert_input)\n", "        pred_cls = outputs.last_hidden_state[:, 0, :]\n", "\n", "        return self.classifier(pred_cls)\n", "\n", "\n", "CUDA0 = \"cuda:0\"\n", "seed = 0\n", "batch_size = 64\n", "num_workers = 4\n", "n_folds = 5\n", "\n", "universe_csv_path = Path(\"/kaggle/input/cmi-precompute/pytorch/all/1/tof-1_raw.csv\")\n", "\n", "deterministic = kagglehub.package_import('wasupandceacar/deterministic').deterministic\n", "deterministic.init_all(seed)\n", "def init_dataset():\n", "    dataset_config = {\n", "        \"percent\": 95,\n", "        \"scaler_function\": StandardScaler(),\n", "        \"nan_ratio\": {\n", "            \"imu\": 0,\n", "            \"thm\": 0,\n", "            \"tof\": 0,\n", "        },\n", "        \"fbfill\": {\n", "            \"imu\": True,\n", "            \"thm\": True,\n", "            \"tof\": True,\n", "        },\n", "        \"one_scale\": True,\n", "        \"tof_raw\": True,\n", "        \"tof_mode\": 16,\n", "        \"save_precompute\": <PERSON><PERSON><PERSON>,\n", "    }\n", "    dataset = CMIFoldDataset(universe_csv_path, dataset_config,\n", "                             n_folds=n_folds, random_seed=seed, full_dataset_function=CMIFeDataset)\n", "    dataset.print_fold_stats()\n", "    return dataset\n", "\n", "def get_fold_dataset(dataset, fold):\n", "    _, valid_dataset = dataset.get_fold_datasets(fold)\n", "    valid_loader = DataLoader(valid_dataset, batch_size=batch_size, num_workers=num_workers, shuffle=False)\n", "    return valid_loader\n", "\n", "dataset = init_dataset()\n", "\n", "model_function = CMIModel\n", "model_args = {\"feat_dim\": 500,\n", "              \"imu1_channels\": 219, \"imu1_dropout\": 0.2946731587132302, \"imu2_dropout\": 0.2697745571929592,\n", "              \"imu1_weight_decay\": 0.0014824054650601245, \"imu2_weight_decay\": 0.002742543773142381,\n", "              \"imu1_layers\": 0, \"imu2_layers\": 0,\n", "              \"thm1_channels\": 82, \"thm1_dropout\": 0.2641274454844602, \"thm2_dropout\": 0.302896343020985, \n", "              \"tof1_channels\": 82, \"tof1_dropout\": 0.2641274454844602, \"tof2_dropout\": 0.3028963430209852, \n", "              \"bert_layers\": 8, \"bert_heads\": 10,\n", "              \"cls1_channels\": 937, \"cls2_channels\": 303, \"cls1_dropout\": 0.2281834512100508, \"cls2_dropout\": 0.22502521933558461}\n", "model_args.update({\n", "    \"imu_dim\": dataset.full_dataset.imu_dim, \n", "    \"thm_dim\": dataset.full_dataset.thm_dim,\n", "    \"tof_dim\": dataset.full_dataset.tof_dim,\n", "    \"n_classes\": dataset.full_dataset.class_num})\n", "model_dir = Path(\"/kaggle/input/cmi-models-public/pytorch/train_fold_model05_tof16_raw/1\")\n", "\n", "model_dicts = [\n", "    {\n", "        \"model_function\": model_function,\n", "        \"model_args\": model_args,\n", "        \"model_path\": model_dir / f\"fold{fold}/best_ema.pt\",\n", "    } for fold in range(n_folds)\n", "]\n", "\n", "models2 = list()\n", "for model_dict in model_dicts:\n", "    model_function = model_dict[\"model_function\"]\n", "    model_args = model_dict[\"model_args\"]\n", "    model_path = model_dict[\"model_path\"]\n", "    model = model_function(**model_args).to(CUDA0)\n", "    state_dict = {k.replace(\"_orig_mod.\", \"\"): v for k, v in torch.load(model_path).items()}\n", "    model.load_state_dict(state_dict)\n", "    model = model.eval()\n", "    models2.append(model)\n", "\n", "\n", "metric_package = kagglehub.package_import('wasupandceacar/cmi-metric')\n", "\n", "metric = metric_package.Metric()\n", "imu_only_metric = metric_package.Metric()\n", "\n", "def to_cuda(*tensors):\n", "    return [tensor.to(CUDA0) for tensor in tensors]\n", "\n", "def predict_valid(model, imu, thm, tof):\n", "    pred = model(imu, thm, tof)\n", "    return pred\n", "\n", "def valid(model, valid_bar):\n", "    with torch.no_grad():\n", "        for imu, thm, tof, y in valid_bar:\n", "            imu, thm, tof, y = to_cuda(imu, thm, tof, y)\n", "            with autocast(device_type='cuda', dtype=torch.bfloat16): \n", "                logits = predict_valid(model, imu, thm, tof)\n", "            metric.add(dataset.le.classes_[y.argmax(dim=1).cpu()], dataset.le.classes_[logits.argmax(dim=1).cpu()])\n", "            _, thm, tof = dataset.full_dataset.get_scaled_nan_tensors(imu, thm, tof)\n", "            with autocast(device_type='cuda', dtype=torch.bfloat16): \n", "                logits = model(imu, thm, tof)\n", "            imu_only_metric.add(dataset.le.classes_[y.argmax(dim=1).cpu()], dataset.le.classes_[logits.argmax(dim=1).cpu()])\n", "\n", "# for fold, model in enumerate(models2):\n", "#     valid_loader = get_fold_dataset(dataset, fold)\n", "#     valid_bar = tqdm(valid_loader, desc=f\"Valid\", position=0, leave=False)\n", "#     valid(model, valid_bar)\n", "\n", "# print(f\"\"\"\n", "# Normal score: {metric.score()}\n", "# IMU only score: {imu_only_metric.score()}\n", "# \"\"\")\n", "\n", "def avg_predict(models, imu, thm, tof):\n", "    outputs = []\n", "    with autocast(device_type='cuda'):\n", "        for model in models:\n", "            logits = model(imu, thm, tof)\n", "        outputs.append(logits)\n", "    return torch.mean(torch.stack(outputs), dim=0)"]}, {"cell_type": "code", "execution_count": 4, "id": "d46faf96", "metadata": {"_cell_guid": "4ec7ba83-bd38-4944-acc5-c569d0b22991", "_uuid": "9ee5932b-ffff-47c4-b5b3-abb1efdfab5c", "collapsed": false, "execution": {"iopub.execute_input": "2025-07-16T03:13:32.165134Z", "iopub.status.busy": "2025-07-16T03:13:32.164913Z", "iopub.status.idle": "2025-07-16T03:13:32.169148Z", "shell.execute_reply": "2025-07-16T03:13:32.168445Z"}, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 0.010406, "end_time": "2025-07-16T03:13:32.170314", "exception": false, "start_time": "2025-07-16T03:13:32.159908", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def predict2(sequence: pl.DataFrame, demographics: pl.DataFrame) -> str:\n", "    imu, thm, tof = dataset.full_dataset.inference_process(sequence)\n", "    with torch.no_grad():\n", "        imu, thm, tof = to_cuda(imu, thm, tof)\n", "        logits = avg_predict(models2, imu, thm, tof)\n", "        probabilities = F.softmax(logits, dim=1).cpu().numpy()\n", "    return probabilities # logits.cpu().numpy()\n", "    # return dataset.le.classes_[logits.argmax(dim=1).cpu()]"]}, {"cell_type": "code", "execution_count": 5, "id": "b8cb8cfd", "metadata": {"_cell_guid": "ebed9297-3da7-4078-8492-90a076566622", "_uuid": "b7b34edb-f179-4043-8232-1ddf88bfefba", "collapsed": false, "execution": {"iopub.execute_input": "2025-07-16T03:13:32.180026Z", "iopub.status.busy": "2025-07-16T03:13:32.179462Z", "iopub.status.idle": "2025-07-16T03:13:32.183241Z", "shell.execute_reply": "2025-07-16T03:13:32.182549Z"}, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 0.009645, "end_time": "2025-07-16T03:13:32.184320", "exception": false, "start_time": "2025-07-16T03:13:32.174675", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def predict(sequence, demographics):\n", "    pred1 = predict1(sequence, demographics)\n", "    pred2 = predict2(sequence, demographics)\n", "    avg_pred = pred1 * 0.4 + pred2 * 0.6\n", "    return dataset.le.classes_[avg_pred.argmax()]"]}, {"cell_type": "code", "execution_count": 6, "id": "98024138", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T03:13:32.193476Z", "iopub.status.busy": "2025-07-16T03:13:32.193284Z", "iopub.status.idle": "2025-07-16T03:13:32.196115Z", "shell.execute_reply": "2025-07-16T03:13:32.195617Z"}, "papermill": {"duration": 0.008564, "end_time": "2025-07-16T03:13:32.197093", "exception": false, "start_time": "2025-07-16T03:13:32.188529", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter(\"ignore\")"]}, {"cell_type": "code", "execution_count": 7, "id": "f5a397c4", "metadata": {"_cell_guid": "d950fdf3-997f-4502-92ff-83a5d2014717", "_uuid": "46f1f772-fc94-4243-b3bd-d7b91e64b825", "collapsed": false, "execution": {"iopub.execute_input": "2025-07-16T03:13:32.206302Z", "iopub.status.busy": "2025-07-16T03:13:32.206101Z", "iopub.status.idle": "2025-07-16T03:13:56.875930Z", "shell.execute_reply": "2025-07-16T03:13:56.875113Z"}, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 24.675973, "end_time": "2025-07-16T03:13:56.877291", "exception": false, "start_time": "2025-07-16T03:13:32.201318", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-16 03:13:33.335308: E tensorflow/core/framework/node_def_util.cc:676] NodeDef mentions attribute use_unbounded_threadpool which is not in the op definition: Op<name=MapDataset; signature=input_dataset:variant, other_arguments: -> handle:variant; attr=f:func; attr=Targuments:list(type),min=0; attr=output_types:list(type),min=1; attr=output_shapes:list(shape),min=1; attr=use_inter_op_parallelism:bool,default=true; attr=preserve_cardinality:bool,default=false; attr=force_synchronous:bool,default=false; attr=metadata:string,default=\"\"> This may be expected if your graph generating binary is newer  than this binary. Unknown attributes will be ignored. NodeDef: {{node ParallelMapDatasetV2/_14}}\n", "I0000 00:00:1752635614.340386      63 cuda_dnn.cc:529] Loaded cuDNN version 90300\n", "2025-07-16 03:13:39.228927: E tensorflow/core/framework/node_def_util.cc:676] NodeDef mentions attribute use_unbounded_threadpool which is not in the op definition: Op<name=MapDataset; signature=input_dataset:variant, other_arguments: -> handle:variant; attr=f:func; attr=Targuments:list(type),min=0; attr=output_types:list(type),min=1; attr=output_shapes:list(shape),min=1; attr=use_inter_op_parallelism:bool,default=true; attr=preserve_cardinality:bool,default=false; attr=force_synchronous:bool,default=false; attr=metadata:string,default=\"\"> This may be expected if your graph generating binary is newer  than this binary. Unknown attributes will be ignored. NodeDef: {{node ParallelMapDatasetV2/_14}}\n", "2025-07-16 03:13:44.456948: E tensorflow/core/framework/node_def_util.cc:676] NodeDef mentions attribute use_unbounded_threadpool which is not in the op definition: Op<name=MapDataset; signature=input_dataset:variant, other_arguments: -> handle:variant; attr=f:func; attr=Targuments:list(type),min=0; attr=output_types:list(type),min=1; attr=output_shapes:list(shape),min=1; attr=use_inter_op_parallelism:bool,default=true; attr=preserve_cardinality:bool,default=false; attr=force_synchronous:bool,default=false; attr=metadata:string,default=\"\"> This may be expected if your graph generating binary is newer  than this binary. Unknown attributes will be ignored. NodeDef: {{node ParallelMapDatasetV2/_14}}\n", "2025-07-16 03:13:50.286832: E tensorflow/core/framework/node_def_util.cc:676] NodeDef mentions attribute use_unbounded_threadpool which is not in the op definition: Op<name=MapDataset; signature=input_dataset:variant, other_arguments: -> handle:variant; attr=f:func; attr=Targuments:list(type),min=0; attr=output_types:list(type),min=1; attr=output_shapes:list(shape),min=1; attr=use_inter_op_parallelism:bool,default=true; attr=preserve_cardinality:bool,default=false; attr=force_synchronous:bool,default=false; attr=metadata:string,default=\"\"> This may be expected if your graph generating binary is newer  than this binary. Unknown attributes will be ignored. NodeDef: {{node ParallelMapDatasetV2/_14}}\n", "2025-07-16 03:13:55.346293: E tensorflow/core/framework/node_def_util.cc:676] NodeDef mentions attribute use_unbounded_threadpool which is not in the op definition: Op<name=MapDataset; signature=input_dataset:variant, other_arguments: -> handle:variant; attr=f:func; attr=Targuments:list(type),min=0; attr=output_types:list(type),min=1; attr=output_shapes:list(shape),min=1; attr=use_inter_op_parallelism:bool,default=true; attr=preserve_cardinality:bool,default=false; attr=force_synchronous:bool,default=false; attr=metadata:string,default=\"\"> This may be expected if your graph generating binary is newer  than this binary. Unknown attributes will be ignored. NodeDef: {{node ParallelMapDatasetV2/_14}}\n"]}], "source": ["import kaggle_evaluation.cmi_inference_server\n", "inference_server = kaggle_evaluation.cmi_inference_server.CMIInferenceServer(predict)\n", "\n", "if os.getenv('KAGGLE_IS_COMPETITION_RERUN'):\n", "    inference_server.serve()\n", "else:\n", "    inference_server.run_local_gateway(\n", "        data_paths=(\n", "            '/kaggle/input/cmi-detect-behavior-with-sensor-data/test.csv',\n", "            '/kaggle/input/cmi-detect-behavior-with-sensor-data/test_demographics.csv',\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": 8, "id": "f2c0856c", "metadata": {"_cell_guid": "ea6c2fad-c06f-4df6-b86c-5338a51caa03", "_uuid": "98acb9ec-5d49-4d58-9dac-a77f817acb3b", "collapsed": false, "execution": {"iopub.execute_input": "2025-07-16T03:13:56.889131Z", "iopub.status.busy": "2025-07-16T03:13:56.888596Z", "iopub.status.idle": "2025-07-16T03:13:56.981281Z", "shell.execute_reply": "2025-07-16T03:13:56.980541Z"}, "jupyter": {"outputs_hidden": false}, "papermill": {"duration": 0.099764, "end_time": "2025-07-16T03:13:56.982460", "exception": false, "start_time": "2025-07-16T03:13:56.882696", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  sequence_id              gesture\n", "0  SEQ_000001  Eyebrow - pull hair\n", "1  SEQ_000011  Eyelash - pull hair\n"]}], "source": ["if not os.getenv('KAGGLE_IS_COMPETITION_RERUN'):\n", "    print(pd.read_parquet(\"submission.parquet\"))"]}], "metadata": {"kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [{"databundleVersionId": 12518947, "sourceId": 102335, "sourceType": "competition"}, {"datasetId": 7645099, "sourceId": 12139340, "sourceType": "datasetVersion"}, {"datasetId": 7748073, "sourceId": 12293285, "sourceType": "datasetVersion"}, {"datasetId": 7827797, "sourceId": 12411879, "sourceType": "datasetVersion"}, {"sourceId": 240649816, "sourceType": "kernelVersion"}, {"sourceId": 246893721, "sourceType": "kernelVersion"}, {"sourceId": 249959489, "sourceType": "kernelVersion"}, {"modelId": 398856, "modelInstanceId": 379625, "sourceId": 470587, "sourceType": "modelInstanceVersion"}, {"modelId": 400086, "modelInstanceId": 380358, "sourceId": 471764, "sourceType": "modelInstanceVersion"}], "dockerImageVersionId": 31090, "isGpuEnabled": true, "isInternetEnabled": false, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}, "papermill": {"default_parameters": {}, "duration": 223.186557, "end_time": "2025-07-16T03:14:00.841059", "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2025-07-16T03:10:17.654502", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}